import { openai } from "@ai-sdk/openai";
import { deepseek } from "@ai-sdk/deepseek";
import { generateObject } from "ai";
import { z } from "zod";

// Input interface for task generation
export interface InputTasksGeneration {
    title: string;
    description: string;
    level: number;
    minutes: number;
    skills?: string[];
    categories?: string[];
}

// Task schema
const TaskSchema = z.object({
    title: z.string(),
    description: z.string(),
    level: z.number().min(1).max(3),
    prompt: z.string(),
    example: z.string()
});

// Output schema for tasks generation
const TasksGenerationSchema = z.object({
    tasks: z.array(TaskSchema).length(3)
});

export type Task = z.infer<typeof TaskSchema>;
export type OutputTasksGeneration = z.infer<typeof TasksGenerationSchema>;

// Create system prompt for LLM
const createSystemPrompt = () => `You are an expert at creating job simulation tasks. Your task is to generate 3 tasks with increasing difficulty levels based on job information.

Guidelines:
- Generate exactly 3 tasks with increasing difficulty levels (1, 2, 3)
- Tasks should be designed for email response format (plain text submission, no file attachments required)
- Total estimated time for all 3 tasks should approximately match the job's total minutes
- Difficulty progression should be relative to the job's level. For examples:
  * For Junior jobs: tasks can go from level 1→2→3 (task 3 may touch Senior level concepts)
  * For Senior jobs: tasks can go from level 2→3→3 (appropriate for Senior level)

Task Structure Requirements:
- title: Clear, concise task title
- description: Markdown format, no emojis or icons. Must contain exactly 2 sections with h3 headings:
  * "### Description" - Explains the task context and requirements
  * "### What you need to do" - Specific instructions for completing and submitting the task
- level: Difficulty level (1-3, increasing)
- prompt: Markdown format instructions for AI verification. Should specify what key points need to be verified in user submissions
- example: A sample solution in markdown format that would pass AI verification

Example Task Structure:
{
  "title": "Analyze Customer Feedback Data",
  "description": "### Description\n\nYou need to review customer feedback data and identify key trends...\n\n### What you need to do\n\nAnalyze the provided data and submit your findings via email response...",
  "level": 1,
  "prompt": "Verify that the user's submission includes:\n- Analysis of key trends\n- Specific data points mentioned\n- Actionable recommendations",
  "example": "Based on the customer feedback analysis:\n\n**Key Trends Identified:**\n- 65% of complaints relate to delivery delays\n- Customer satisfaction dropped 15% in Q3\n\n**Recommendations:**\n- Implement faster shipping options\n- Improve communication during delays"
}

Time Distribution:
- Distribute the total job minutes across 3 tasks appropriately
- Task 1 (easiest): ~25-30% of total time
- Task 2 (medium): ~30-35% of total time
- Task 3 (hardest): ~35-40% of total time`;

// Create user prompt with job data
const createUserPrompt = (jobData: InputTasksGeneration) => {
    const skillsText = jobData.skills && jobData.skills.length > 0
        ? `\nRequired Skills: ${jobData.skills.join(', ')}`
        : '';

    const categoriesText = jobData.categories && jobData.categories.length > 0
        ? `\nCategories: ${jobData.categories.join(', ')}`
        : '';

    return `Generate 3 simulation tasks for this job:

Job Title: ${jobData.title}
Description: ${jobData.description}
Job Level: ${jobData.level} (1=easy, 2=medium, 3=hard)
Total Time: ${jobData.minutes} minutes${skillsText}${categoriesText}

Please create 3 tasks with increasing difficulty that total approximately ${jobData.minutes} minutes of work time.`;
};

/**
 * Generate tasks from job data using LLM
 * Prioritizes Deepseek, falls back to OpenAI if Deepseek fails
 */
export async function generateTasks(jobData: InputTasksGeneration): Promise<OutputTasksGeneration> {
    const systemPrompt = createSystemPrompt();
    const userPrompt = createUserPrompt(jobData);

    // Try Deepseek first
    try {
        console.log('Attempting to generate tasks using Deepseek...');

        const result = await generateObject({
            model: deepseek('deepseek-chat'),
            system: systemPrompt,
            prompt: userPrompt,
            schema: TasksGenerationSchema,
            temperature: 0.7,
        });

        console.log('Successfully generated tasks using Deepseek');
        return result.object;
    } catch (deepseekError) {
        console.warn('Deepseek failed, falling back to OpenAI:', deepseekError);

        // Fallback to OpenAI
        try {
            console.log('Attempting to generate tasks using OpenAI...');

            const result = await generateObject({
                model: openai('gpt-4o-mini'),
                system: systemPrompt,
                prompt: userPrompt,
                schema: TasksGenerationSchema,
                temperature: 0.7,
            });

            console.log('Successfully generated tasks using OpenAI');
            return result.object;
        } catch (openaiError) {
            console.error('Both Deepseek and OpenAI failed:', { deepseekError, openaiError });
            throw new Error(`Failed to generate tasks: Deepseek error: ${deepseekError}, OpenAI error: ${openaiError}`);
        }
    }
}