import { openai } from "@ai-sdk/openai";
import { deepseek } from "@ai-sdk/deepseek";
import { generateObject } from "ai";
import { z } from "zod";

/**
 * TODO: Từ thông tin 1 job thực tế, hãy generate ra danh sách 3 tasks cho job simulation với độ khó tăng dần. Các task này sẽ được user submit bằng plain text bằng cách giả lập trả lời email của người quản lý, nên chỉ thiết kế những task đừng quá phức tạp, task phải có thông tin rõ ràng, không yêu cầu đính kèm file.
 * 
 * Input:
 * - title: String. Required. Tiêu đề của job.
 * - description: String. Required. Mô tả của job.
 * - level: Number. Required. Đ<PERSON> khó của job, từ 1 đến 3, với 1 l<PERSON> d<PERSON> nhất, 2 là trung bình, 3 là kh<PERSON> nhất.
 * - minutes: Number. Required. Thời gian để hoàn thành job, tính bằng phút.
 * - skills: String[]. Optional. Danh sách các skills liên quan đến job.
 * - categories: String[]. Optional. Danh sách các categories liên quan đến job.
 * 
 * Ouput:
 * - tasks: Task[]. Danh sách các tasks. Mỗi task có các thông tin sau:
 *    - title: String. Tiêu đề của task.
 *    - description: String. Mô tả của task. Format markdown, không dùng emojis, icons. Gồm 2 phần: Description và What you need to do. Description mô tả thông tin task và What you need to do sẽ mô tả cách user làm task và submit
 *    - level: Number. Độ khó của task, từ 1 đến 3, với 1 là dễ nhất, 2 là trung bình, 3 là khó nhất.
 *    - prompt: String. Đoạn prompt cho tính năng verify task bằng AI. Format markdown, không dùng emojis, icons.
 */